Component({
  /**
   * 组件的属性列表
   */
  properties: {
    show: {
      type: Boolean,
      value: false
    },
    title: {
      type: String,
      value: '修改服务时间'
    },
    initialTime: {
      type: Object,
      value: {}
    },
    loadingText: {
      type: String,
      value: '修改中...'
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    timeForm: {
      serviceTime: ''
    },
    // 时间选择器相关
    timeArray: [0, 0, 0, 0],
    timeRange: [
      [], // 年份
      [], // 月份
      [], // 日期
      [] // 时间（半小时间隔）
    ],
    showTimePicker: false
  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 初始化时间选择器
     */
    initTimePicker() {
      const currentDate = new Date();
      const currentYear = currentDate.getFullYear();
      const currentMonth = currentDate.getMonth() + 1;
      const currentDay = currentDate.getDate();

      // 生成年份范围（当前年份到未来2年）
      const years = [];
      for (let year = currentYear; year <= currentYear + 2; year++) {
        years.push(year);
      }

      // 生成月份范围
      const months = Array.from({ length: 12 }, (_, i) => i + 1);

      // 生成日期范围（根据选择的年月）
      const days = Array.from({
        length: this.getDaysInMonth(currentYear, currentMonth)
      }, (_, i) => i + 1);

      // 生成半小时间隔
      const times = [];
      for (let hour = 8; hour < 22; hour++) { // 服务时间8:00-22:00
        times.push(`${hour.toString().padStart(2, '0')}:00`);
        times.push(`${hour.toString().padStart(2, '0')}:30`);
      }

      this.setData({
        timeRange: [years, months, days, times],
        timeArray: [0, currentMonth - 1, currentDay - 1, 0]
      });
    },

    /**
     * 获取月份的天数
     */
    getDaysInMonth(year, month) {
      return new Date(year, month, 0).getDate();
    },

    /**
     * 时间选择器变化
     */
    bindTimeChange(e) {
      const { value } = e.detail;
      this.setData({
        timeArray: value
      });
    },

    /**
     * 处理列变化，用于动态更新日期
     */
    bindTimeColumnChange(e) {
      const { column, value } = e.detail;
      const timeRange = [...this.data.timeRange];
      const timeArray = [...this.data.timeArray];

      if (column === 0 || column === 1) {
        // 更新年份或月份时，重新计算日期
        timeArray[column] = value;
        const selectedYear = timeRange[0][timeArray[0]];
        const selectedMonth = timeRange[1][timeArray[1]];
        const updatedDays = this.getDaysInMonth(selectedYear, selectedMonth);

        timeRange[2] = Array.from({ length: updatedDays }, (_, i) => i + 1);

        // 如果当前选择的日期超出了新月份的天数，调整到最后一天
        if (timeArray[2] >= updatedDays) {
          timeArray[2] = updatedDays - 1;
        }

        this.setData({
          timeRange,
          timeArray
        });
      }
    },

    /**
     * 显示时间选择器
     */
    showTimePickerModal() {
      this.setData({ showTimePicker: true });
    },

    /**
     * 隐藏时间选择器
     */
    hideTimePickerModal() {
      this.setData({ showTimePicker: false });
    },

    /**
     * 确认时间选择
     */
    confirmTimeSelection() {
      const { timeArray, timeRange } = this.data;
      const [yearIndex, monthIndex, dayIndex, timeIndex] = timeArray;
      const selectedYear = timeRange[0][yearIndex];
      const selectedMonth = timeRange[1][monthIndex];
      const selectedDay = timeRange[2][dayIndex];
      const selectedTime = timeRange[3][timeIndex];

      const selectedDateTime = new Date(
        selectedYear,
        selectedMonth - 1,
        selectedDay,
        ...selectedTime.split(':').map(Number)
      );

      const currentDateTime = new Date();

      if (selectedDateTime <= currentDateTime) {
        wx.showToast({
          title: '请选择未来时间',
          icon: 'none'
        });
        return;
      }

      const formattedTime = `${selectedYear}-${selectedMonth.toString().padStart(2, '0')}-${selectedDay.toString().padStart(2, '0')} ${selectedTime}:00`;

      this.setData({
        'timeForm.serviceTime': formattedTime,
        showTimePicker: false
      });
    },

    /**
     * 关闭弹窗
     */
    close() {
      this.triggerEvent('close');
    },

    /**
     * 确认修改
     */
    confirm() {
      const { timeForm } = this.data;

      if (!timeForm.serviceTime) {
        wx.showToast({
          title: '请选择服务时间',
          icon: 'none'
        });
        return;
      }

      this.triggerEvent('confirm', { timeForm });
    },

    /**
     * 格式化显示时间
     */
    formatDisplayTime(timeStr) {
      if (!timeStr) return '请选择时间';
      
      const date = new Date(timeStr);
      const year = date.getFullYear();
      const month = (date.getMonth() + 1).toString().padStart(2, '0');
      const day = date.getDate().toString().padStart(2, '0');
      const hour = date.getHours().toString().padStart(2, '0');
      const minute = date.getMinutes().toString().padStart(2, '0');
      
      return `${year}-${month}-${day} ${hour}:${minute}`;
    }
  },

  /**
   * 组件生命周期
   */
  lifetimes: {
    attached() {
      this.initTimePicker();
    }
  },

  /**
   * 监听属性变化
   */
  observers: {
    'initialTime': function(newVal) {
      if (newVal && newVal.serviceTime) {
        this.setData({
          'timeForm.serviceTime': newVal.serviceTime
        });
      }
    }
  }
});
