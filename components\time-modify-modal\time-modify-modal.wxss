.time-modify-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1000;
}

.modal-mask {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
}

.modal-content {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  background-color: #fff;
  border-radius: 20rpx 20rpx 0 0;
  animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 40rpx 40rpx 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.modal-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
}

.close-btn {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-icon {
  font-size: 40rpx;
  color: #999;
}

.modal-body {
  padding: 40rpx;
}

.form-item {
  margin-bottom: 40rpx;
}

.form-label {
  font-size: 32rpx;
  color: #333;
  margin-bottom: 20rpx;
  font-weight: 500;
}

.time-input {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx 30rpx;
  border: 2rpx solid #e6e6e6;
  border-radius: 12rpx;
  background-color: #fafafa;
}

.time-text {
  font-size: 30rpx;
  color: #333;
  flex: 1;
}

.time-text.placeholder {
  color: #999;
}

.arrow-icon {
  font-size: 28rpx;
  color: #999;
  margin-left: 20rpx;
}

.time-tip {
  margin-top: 30rpx;
  padding: 24rpx;
  background-color: #f8f9fa;
  border-radius: 12rpx;
}

.tip-text {
  display: block;
  font-size: 26rpx;
  color: #666;
  line-height: 1.6;
  margin-bottom: 8rpx;
}

.tip-text:last-child {
  margin-bottom: 0;
}

.modal-footer {
  padding: 20rpx 40rpx 40rpx;
}

.btn-group {
  display: flex;
  gap: 20rpx;
}

.btn {
  flex: 1;
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 12rpx;
  font-size: 32rpx;
  font-weight: 500;
}

.btn-cancel {
  background-color: #f5f5f5;
  color: #666;
}

.btn-confirm {
  background-color: #007aff;
  color: #fff;
}

/* 时间选择器样式 */
.time-picker-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1100;
}

.picker-mask {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
}

.picker-content {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  background-color: #fff;
  border-radius: 20rpx 20rpx 0 0;
  animation: slideUp 0.3s ease-out;
}

.picker-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx 40rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.picker-btn {
  font-size: 32rpx;
  color: #007aff;
  padding: 10rpx 20rpx;
}

.picker-btn.confirm {
  font-weight: 600;
}

.picker-title {
  font-size: 34rpx;
  font-weight: 600;
  color: #333;
}

.picker-view {
  height: 400rpx;
}

.picker-item {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 80rpx;
  font-size: 32rpx;
  color: #333;
}
